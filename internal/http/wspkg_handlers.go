package http

import (
	"database/sql"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/code"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/config"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/database"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/types"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/utils"
	"github.com/Terracode-Dev/ION_SEC_DOOR_WSS/internal/wspkg"
	"github.com/labstack/echo/v4"
)

// WSPkgHttpRoutes handles HTTP routes that integrate with the new wspkg WebSocket package
type WSPkgHttpRoutes struct {
	wsManager *wspkg.Manager
	query     *database.Queries
	cfg       *config.Config
}

// NewWSPkgHttpRoutes creates a new instance of WSPkgHttpRoutes
func NewWSPkgHttpRoutes(wsManager *wspkg.Manager, query *database.Queries, cfg *config.Config) *WSPkgHttpRoutes {
	return &WSPkgHttpRoutes{
		wsManager: wsManager,
		query:     query,
		cfg:       cfg,
	}
}

// DoorClientState represents the simplified door client state for wspkg
type DoorClientState struct {
	ID          string
	IsOpen      bool
	QRInClient  *QRClientState
	QROutClient *QRClientState
	Processing  bool
	BranchID    int
}

// QRClientState represents the simplified QR client state for wspkg
type QRClientState struct {
	ID       string
	Type     string // "qr-in" or "qr-out"
	BranchID int
}

// GetDoorClient retrieves a door client by ID and validates it's a door type
func (h *WSPkgHttpRoutes) GetDoorClient(deviceID string) (*DoorClientState, error) {
	client, exists := h.wsManager.GetClient(deviceID)
	if !exists || client == nil {
		return nil, fmt.Errorf("door client %s not found", deviceID)
	}

	if !client.IsOnline() {
		return nil, fmt.Errorf("door client %s is offline", deviceID)
	}

	// For now, create a simplified door state
	// In a full implementation, you'd store this state in the client's metadata
	doorState := &DoorClientState{
		ID:         deviceID,
		IsOpen:     false, // Default state
		Processing: false,
		BranchID:   0, // Would be set from client metadata
	}

	return doorState, nil
}

// GetQRClient retrieves a QR client by ID and validates it's a QR type
func (h *WSPkgHttpRoutes) GetQRClient(clientID string) (*QRClientState, error) {
	client, exists := h.wsManager.GetClient(clientID)
	if !exists || client == nil {
		return nil, fmt.Errorf("QR client %s not found", clientID)
	}

	if !client.IsOnline() {
		return nil, fmt.Errorf("QR client %s is offline", clientID)
	}

	// For now, create a simplified QR state
	// In a full implementation, you'd store this state in the client's metadata
	qrState := &QRClientState{
		ID:       clientID,
		Type:     "qr-in", // Would be determined from client metadata
		BranchID: 0,       // Would be set from client metadata
	}

	return qrState, nil
}

// SendProcessingMessage sends a processing status message to QR clients
func (h *WSPkgHttpRoutes) SendProcessingMessage(qrInClient, qrOutClient *QRClientState, processing bool) error {
	status := "true"
	if !processing {
		status = "false"
	}

	message := wspkg.NewMessage("processing", []byte(fmt.Sprintf(`{"isProcessing": "%s"}`, status)))

	var errors []string

	if qrInClient != nil {
		client, exists := h.wsManager.GetClient(qrInClient.ID)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			log.Printf("[WSPkg] Sent processing message to QR-in client %s", qrInClient.ID)
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrInClient.ID))
		}
	}

	if qrOutClient != nil {
		client, exists := h.wsManager.GetClient(qrOutClient.ID)
		if exists && client != nil && client.IsOnline() {
			client.Send(message)
			log.Printf("[WSPkg] Sent processing message to QR-out client %s", qrOutClient.ID)
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOutClient.ID))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send processing messages: %v", errors)
	}

	return nil
}

// SendUnlockMessage sends an unlock message to the door client
func (h *WSPkgHttpRoutes) SendUnlockMessage(doorClient *DoorClientState) error {
	client := h.wsManager.GetClient(doorClient.ID)
	if client == nil || !client.IsOnline() {
		return fmt.Errorf("door client %s not available", doorClient.ID)
	}

	message := wspkg.NewMessage("unlock", []byte(`{"isUnlock": true}`))
	client.Send(message)

	log.Printf("[WSPkg] Sent unlock message to door client %s", doorClient.ID)
	return nil
}

// SendSuccessMessages sends success messages to QR clients
func (h *WSPkgHttpRoutes) SendSuccessMessages(qrInClient, qrOutClient *QRClientState) error {
	successMessage := wspkg.NewMessage("unlock-success", []byte(`{"status": "success", "message": "Door opened successfully"}`))

	var errors []string

	if qrInClient != nil {
		client := h.wsManager.GetClient(qrInClient.ID)
		if client != nil && client.IsOnline() {
			client.Send(successMessage)
			log.Printf("[WSPkg] Sent success message to QR-in client %s", qrInClient.ID)
		} else {
			errors = append(errors, fmt.Sprintf("QR-in client %s not available", qrInClient.ID))
		}
	}

	if qrOutClient != nil {
		client := h.wsManager.GetClient(qrOutClient.ID)
		if client != nil && client.IsOnline() {
			client.Send(successMessage)
			log.Printf("[WSPkg] Sent success message to QR-out client %s", qrOutClient.ID)
		} else {
			errors = append(errors, fmt.Sprintf("QR-out client %s not available", qrOutClient.ID))
		}
	}

	if len(errors) > 0 {
		log.Printf("[WSPkg] Some success messages failed to send: %v", errors)
		// Don't return error for success messages as they're not critical
	}

	return nil
}

// SendGenerateNewMessage sends a generate-new message to the requesting QR client
func (h *WSPkgHttpRoutes) SendGenerateNewMessage(clientID string) error {
	client := h.wsManager.GetClient(clientID)
	if client == nil || !client.IsOnline() {
		return fmt.Errorf("QR client %s not available", clientID)
	}

	message := wspkg.NewMessage("generate-new", []byte(`{"status": "success", "refresh": true}`))
	client.Send(message)

	log.Printf("[WSPkg] Sent generate-new message to QR client %s", clientID)
	return nil
}

// SendErrorMessages sends error messages to QR clients
func (h *WSPkgHttpRoutes) SendErrorMessages(qrInClient, qrOutClient *QRClientState, errorMsg string) {
	errorMessage := wspkg.NewMessage("processError", []byte(fmt.Sprintf(`{"isProcessing": false, "error": "%s"}`, errorMsg)))

	if qrInClient != nil {
		client := h.wsManager.GetClient(qrInClient.ID)
		if client != nil && client.IsOnline() {
			client.Send(errorMessage)
			log.Printf("[WSPkg] Sent error message to QR-in client %s", qrInClient.ID)
		}
	}

	if qrOutClient != nil {
		client := h.wsManager.GetClient(qrOutClient.ID)
		if client != nil && client.IsOnline() {
			client.Send(errorMessage)
			log.Printf("[WSPkg] Sent error message to QR-out client %s", qrOutClient.ID)
		}
	}
}

// OpenDoorWSPkg is the new simplified open door endpoint using wspkg
func (h *WSPkgHttpRoutes) OpenDoorWSPkg(c echo.Context) error {
	var req types.DoorLockAccessReq
	err := c.Bind(&req)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Request binding failed: %v", err)
		return c.JSON(http.StatusBadRequest, "request is not valid")
	}

	log.Printf("[OpenDoorWSPkg] Processing request for user ID: %d, QR code length: %d", req.User.ID, len(req.QRcode))

	// Validate JWT token and extract QR code info
	claim, err := utils.ValidateTokenWithSecret(req.QRcode, h.cfg.JWTSecret)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Invalid QR token: %v", err)
		return c.JSON(http.StatusUnauthorized, "invalid qr code")
	}
	qrData := claim.QRInfo

	log.Printf("[OpenDoorWSPkg] QR Data extracted - ReqDeviceId: %s, ClientId: %s, ConnType: %s, BranchId: %d",
		qrData.ReqDeviceId, qrData.ClientId, qrData.ConnType, qrData.BranchId)

	// Validate that the target device is connected
	doorClient, err := h.GetDoorClient(qrData.ReqDeviceId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] %v", err)
		return c.JSON(http.StatusBadRequest, "target device not connected")
	}

	// Validate QR client exists and is connected
	qrClient, err := h.GetQRClient(qrData.ClientId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] %v", err)
		return c.JSON(http.StatusBadRequest, "QR client not connected")
	}

	// Validate QR type matches
	if qrClient.Type != qrData.ConnType {
		log.Printf("[ERROR - OpenDoorWSPkg] QR type mismatch: expected %s, got %s", qrClient.Type, qrData.ConnType)
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.CLIENTINVALIED, "QR type mismatch"))
	}

	log.Printf("[OpenDoorWSPkg] QR client %s validated successfully", qrData.ClientId)

	// Set processing status for QR clients
	// For simplicity, we'll assume we have both qr-in and qr-out clients
	// In a real implementation, you'd track which clients are associated with this door
	var qrInClient, qrOutClient *QRClientState
	if qrData.ConnType == "qr-in" {
		qrInClient = qrClient
	} else {
		qrOutClient = qrClient
	}

	// Send processing messages to QR clients
	err = h.SendProcessingMessage(qrInClient, qrOutClient, true)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send processing messages: %v", err)
		return c.JSON(http.StatusInternalServerError, "failed to send processing message")
	}

	// Set door processing state
	doorClient.Processing = true

	// Check if user exists, create if not
	var userID int64
	userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Create new user
			err := h.query.InsertDoorLockUser(c.Request().Context(), database.InsertDoorLockUserParams{
				UserID:    int64(req.User.ID),
				Username:  req.User.Name,
				BranchID:  int64(qrData.BranchId),
				Email:     sql.NullString{String: req.User.Email, Valid: true},
				Role:      sql.NullString{String: req.User.Role, Valid: true},
				Phone:     sql.NullString{String: req.User.Phone, Valid: true},
				Nic:       sql.NullString{String: req.User.NIC, Valid: true},
				AvatarUrl: sql.NullString{String: req.User.AvatarURL, Valid: true},
			})
			if err != nil {
				log.Printf("[ERROR - OpenDoorWSPkg] Failed to insert user: %v", err)
				h.SendErrorMessages(qrInClient, qrOutClient, "failed to create user")
				return c.JSON(http.StatusInternalServerError, "failed to insert user")
			}

			userID, err = h.query.GetAttendeeIDByUserID(c.Request().Context(), int64(req.User.ID))
			if err != nil {
				log.Printf("[ERROR - OpenDoorWSPkg] Failed to get user ID after creation: %v", err)
				h.SendErrorMessages(qrInClient, qrOutClient, "failed to get user id")
				return c.JSON(http.StatusInternalServerError, "failed to get user id")
			}
		} else {
			log.Printf("[ERROR - OpenDoorWSPkg] Database error: %v", err)
			h.SendErrorMessages(qrInClient, qrOutClient, "user lookup failed")
			return c.JSON(http.StatusBadRequest, "user not found")
		}
	}

	// Check attendance logic
	timeNow := time.Now()
	dateOnly := time.Date(timeNow.Year(), timeNow.Month(), timeNow.Day(), 0, 0, 0, 0, time.UTC)

	err = h.validateAttendanceLogic(c.Request().Context(), req.User, qrData, dateOnly, qrInClient, qrOutClient)
	if err != nil {
		return err // Error response already sent in validateAttendanceLogic
	}

	// Check if door is already open
	if doorClient.IsOpen {
		h.SendErrorMessages(qrInClient, qrOutClient, "door already open")
		return c.JSON(http.StatusBadRequest, types.NewAppResponseMsg(code.DoorOpen, "door open already"))
	}

	// Send unlock message to door
	err = h.SendUnlockMessage(doorClient)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send unlock message: %v", err)
		h.SendErrorMessages(qrInClient, qrOutClient, "failed to unlock door")
		return c.JSON(http.StatusInternalServerError, "failed to send unlock message")
	}

	// Create attendance record
	err = h.createAttendanceRecord(c.Request().Context(), userID, req.User, qrData)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to create attendance record: %v", err)
		// Don't fail the request for attendance record creation failure
	}

	// Send success messages
	err = h.SendSuccessMessages(qrInClient, qrOutClient)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send success messages: %v", err)
		// Don't fail the request for success message failure
	}

	// Send generate-new message to requesting QR client
	err = h.SendGenerateNewMessage(qrData.ClientId)
	if err != nil {
		log.Printf("[ERROR - OpenDoorWSPkg] Failed to send generate-new message: %v", err)
		// Don't fail the request for generate-new message failure
	}

	log.Printf("[OpenDoorWSPkg] Door opened successfully for user %d via device %s", req.User.ID, qrData.ReqDeviceId)

	return c.JSON(http.StatusOK, map[string]interface{}{
		"code":    true,
		"message": "Door opened successfully",
		"user":    req.User.Name,
		"device":  qrData.ReqDeviceId,
	})
}
